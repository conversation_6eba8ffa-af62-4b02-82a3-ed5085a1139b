// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const app_name = 'app.name';
  static const app_version = 'app.version';
  static const app = 'app';
  static const auth_login_title = 'auth.login.title';
  static const auth_login_subtitle = 'auth.login.subtitle';
  static const auth_login_phone_label = 'auth.login.phone_label';
  static const auth_login_phone_placeholder = 'auth.login.phone_placeholder';
  static const auth_login_phone_validation = 'auth.login.phone_validation';
  static const auth_login_agree_terms = 'auth.login.agree_terms';
  static const auth_login_terms_error = 'auth.login.terms_error';
  static const auth_login_demo_login = 'auth.login.demo_login';
  static const auth_login_login_button = 'auth.login.login_button';
  static const auth_login_send_sms = 'auth.login.send_sms';
  static const auth_login_login_error = 'auth.login.login_error';
  static const auth_login_login_failed = 'auth.login.login_failed';
  static const auth_login_role_supervisor = 'auth.login.role_supervisor';
  static const auth_login_role_seller = 'auth.login.role_seller';
  static const auth_login = 'auth.login';
  static const auth_sms_verification_title = 'auth.sms_verification.title';
  static const auth_sms_verification_subtitle = 'auth.sms_verification.subtitle';
  static const auth_sms_verification_code_placeholder = 'auth.sms_verification.code_placeholder';
  static const auth_sms_verification_verify_button = 'auth.sms_verification.verify_button';
  static const auth_sms_verification_resend_code = 'auth.sms_verification.resend_code';
  static const auth_sms_verification_resend_text = 'auth.sms_verification.resend_text';
  static const auth_sms_verification_verification_error = 'auth.sms_verification.verification_error';
  static const auth_sms_verification_enter_code_title = 'auth.sms_verification.enter_code_title';
  static const auth_sms_verification_enter_code_subtitle = 'auth.sms_verification.enter_code_subtitle';
  static const auth_sms_verification_code_validation_error = 'auth.sms_verification.code_validation_error';
  static const auth_sms_verification_sms_resent_success = 'auth.sms_verification.sms_resent_success';
  static const auth_sms_verification_generic_error = 'auth.sms_verification.generic_error';
  static const auth_sms_verification_verification_failed = 'auth.sms_verification.verification_failed';
  static const auth_sms_verification_unexpected_error = 'auth.sms_verification.unexpected_error';
  static const auth_sms_verification_sending = 'auth.sms_verification.sending';
  static const auth_sms_verification_resend_with_countdown = 'auth.sms_verification.resend_with_countdown';
  static const auth_sms_verification_resend_simple = 'auth.sms_verification.resend_simple';
  static const auth_sms_verification = 'auth.sms_verification';
  static const auth_role_selection_title = 'auth.role_selection.title';
  static const auth_role_selection_subtitle = 'auth.role_selection.subtitle';
  static const auth_role_selection_continue_button = 'auth.role_selection.continue_button';
  static const auth_role_selection_nazoratchi = 'auth.role_selection.nazoratchi';
  static const auth_role_selection_nazoratchi_description = 'auth.role_selection.nazoratchi_description';
  static const auth_role_selection_sotuvchi = 'auth.role_selection.sotuvchi';
  static const auth_role_selection_sotuvchi_description = 'auth.role_selection.sotuvchi_description';
  static const auth_role_selection = 'auth.role_selection';
  static const auth_services_sms_sent = 'auth.services.sms_sent';
  static const auth_services_error_occurred = 'auth.services.error_occurred';
  static const auth_services_login_success = 'auth.services.login_success';
  static const auth_services_invalid_sms_code = 'auth.services.invalid_sms_code';
  static const auth_services_demo_login = 'auth.services.demo_login';
  static const auth_services_no_internet = 'auth.services.no_internet';
  static const auth_services_sms_resent = 'auth.services.sms_resent';
  static const auth_services_sms_send_error = 'auth.services.sms_send_error';
  static const auth_services_server_error = 'auth.services.server_error';
  static const auth_services_general_error = 'auth.services.general_error';
  static const auth_services_connection_timeout = 'auth.services.connection_timeout';
  static const auth_services_receive_timeout = 'auth.services.receive_timeout';
  static const auth_services_connection_error = 'auth.services.connection_error';
  static const auth_services_bad_request = 'auth.services.bad_request';
  static const auth_services_unexpected_error = 'auth.services.unexpected_error';
  static const auth_services = 'auth.services';
  static const auth = 'auth';
  static const navigation_home = 'navigation.home';
  static const navigation_statistics = 'navigation.statistics';
  static const navigation_market_structure = 'navigation.market_structure';
  static const navigation_payment_history = 'navigation.payment_history';
  static const navigation_empty_places = 'navigation.empty_places';
  static const navigation_profile = 'navigation.profile';
  static const navigation = 'navigation';
  static const market_structure_title = 'market_structure.title';
  static const market_structure_error_occurred = 'market_structure.error_occurred';
  static const market_structure_no_pavilions = 'market_structure.no_pavilions';
  static const market_structure_no_blocks = 'market_structure.no_blocks';
  static const market_structure_no_blocks_subtitle = 'market_structure.no_blocks_subtitle';
  static const market_structure = 'market_structure';
  static const profile_men = 'profile.men';
  static const profile_women = 'profile.women';
  static const profile_personal_info = 'profile.personal_info';
  static const profile_personal_info_subtitle = 'profile.personal_info_subtitle';
  static const profile_seller_personal_info_subtitle = 'profile.seller_personal_info_subtitle';
  static const profile_biometric_data = 'profile.biometric_data';
  static const profile_biometric_data_subtitle = 'profile.biometric_data_subtitle';
  static const profile_notifications = 'profile.notifications';
  static const profile_notifications_subtitle = 'profile.notifications_subtitle';
  static const profile_language_selection = 'profile.language_selection';
  static const profile_language_subtitle_uzbek = 'profile.language_subtitle_uzbek';
  static const profile_language_subtitle_russian = 'profile.language_subtitle_russian';
  static const profile_logout = 'profile.logout';
  static const profile_logout_subtitle = 'profile.logout_subtitle';
  static const profile_edit_profile = 'profile.edit_profile';
  static const profile_save_changes = 'profile.save_changes';
  static const profile_cancel = 'profile.cancel';
  static const profile_select_image = 'profile.select_image';
  static const profile_camera = 'profile.camera';
  static const profile_gallery = 'profile.gallery';
  static const profile_first_name = 'profile.first_name';
  static const profile_last_name = 'profile.last_name';
  static const profile_middle_name = 'profile.middle_name';
  static const profile_phone_number = 'profile.phone_number';
  static const profile_gender = 'profile.gender';
  static const profile_address = 'profile.address';
  static const profile_pavilion = 'profile.pavilion';
  static const profile_blocks = 'profile.blocks';
  static const profile_places = 'profile.places';
  static const profile_blocks_count = 'profile.blocks_count';
  static const profile_places_count = 'profile.places_count';
  static const profile_default_user_name = 'profile.default_user_name';
  static const profile_loading_info = 'profile.loading_info';
  static const profile_user_data_not_found = 'profile.user_data_not_found';
  static const profile_authentication_error = 'profile.authentication_error';
  static const profile_image_selection_error = 'profile.image_selection_error';
  static const profile_logout_confirmation = 'profile.logout_confirmation';
  static const profile_logout_error = 'profile.logout_error';
  static const profile_profile_loaded_from_cache = 'profile.profile_loaded_from_cache';
  static const profile_failed_to_load_profile = 'profile.failed_to_load_profile';
  static const profile_profile_refreshed_successfully = 'profile.profile_refreshed_successfully';
  static const profile_failed_to_refresh_profile = 'profile.failed_to_refresh_profile';
  static const profile_failed_to_load_cached_profile = 'profile.failed_to_load_cached_profile';
  static const profile_profile_image_updated_successfully = 'profile.profile_image_updated_successfully';
  static const profile_profile_image_updated = 'profile.profile_image_updated';
  static const profile_profile_image_update_error = 'profile.profile_image_update_error';
  static const profile_old_profile_image_restored = 'profile.old_profile_image_restored';
  static const profile_profile_image_update_and_reload_error = 'profile.profile_image_update_and_reload_error';
  static const profile_old_profile_image_restored_from_cache = 'profile.old_profile_image_restored_from_cache';
  static const profile = 'profile';
  static const language_dialog_title = 'language.dialog_title';
  static const language_uzbek = 'language.uzbek';
  static const language_russian = 'language.russian';
  static const language_english = 'language.english';
  static const language_save = 'language.save';
  static const language_cancel = 'language.cancel';
  static const language = 'language';
  static const home_tariff = 'home.tariff';
  static const home = 'home';
  static const payment_daily_rate = 'payment.daily_rate';
  static const payment_daily_tariff = 'payment.daily_tariff';
  static const payment_category = 'payment.category';
  static const payment_tariff = 'payment.tariff';
  static const payment_place_size = 'payment.place_size';
  static const payment_total_square = 'payment.total_square';
  static const payment_debt = 'payment.debt';
  static const payment_payment_success = 'payment.payment_success';
  static const payment_payment_error = 'payment.payment_error';
  static const payment_cash_payment_title = 'payment.cash_payment_title';
  static const payment_accept_payment = 'payment.accept_payment';
  static const payment_payment_creation_error = 'payment.payment_creation_error';
  static const payment_payment_confirmation_error = 'payment.payment_confirmation_error';
  static const payment_payment_process_error = 'payment.payment_process_error';
  static const payment = 'payment';
  static const places_empty_place = 'places.empty_place';
  static const places_unusable_place = 'places.unusable_place';
  static const places_needs_repair = 'places.needs_repair';
  static const places_dirty_place = 'places.dirty_place';
  static const places_broken_equipment = 'places.broken_equipment';
  static const places_place_number = 'places.place_number';
  static const places_block = 'places.block';
  static const places_pavilion = 'places.pavilion';
  static const places_fresh_fruits = 'places.fresh_fruits';
  static const places_no_empty_places = 'places.no_empty_places';
  static const places_no_empty_places_subtitle = 'places.no_empty_places_subtitle';
  static const places_category = 'places.category';
  static const places_rental_price = 'places.rental_price';
  static const places_contact = 'places.contact';
  static const places_contact_message = 'places.contact_message';
  static const places_place_numbers = 'places.place_numbers';
  static const places_not_found = 'places.not_found';
  static const places_not_found_subtitle = 'places.not_found_subtitle';
  static const places_call = 'places.call';
  static const places = 'places';
  static const common_save = 'common.save';
  static const common_cancel = 'common.cancel';
  static const common_close = 'common.close';
  static const common_ok = 'common.ok';
  static const common_yes = 'common.yes';
  static const common_no = 'common.no';
  static const common_loading = 'common.loading';
  static const common_error = 'common.error';
  static const common_error_occurred = 'common.error_occurred';
  static const common_success = 'common.success';
  static const common_retry = 'common.retry';
  static const common_refresh = 'common.refresh';
  static const common_search = 'common.search';
  static const common_filter = 'common.filter';
  static const common_clear = 'common.clear';
  static const common_no_internet_connection = 'common.no_internet_connection';
  static const common_unknown_seller = 'common.unknown_seller';
  static const common = 'common';
  static const date_picker_dialog_title = 'date_picker.dialog_title';
  static const date_picker_ok = 'date_picker.ok';
  static const date_picker_cancel = 'date_picker.cancel';
  static const date_picker_months_january = 'date_picker.months.january';
  static const date_picker_months_february = 'date_picker.months.february';
  static const date_picker_months_march = 'date_picker.months.march';
  static const date_picker_months_april = 'date_picker.months.april';
  static const date_picker_months_may = 'date_picker.months.may';
  static const date_picker_months_june = 'date_picker.months.june';
  static const date_picker_months_july = 'date_picker.months.july';
  static const date_picker_months_august = 'date_picker.months.august';
  static const date_picker_months_september = 'date_picker.months.september';
  static const date_picker_months_october = 'date_picker.months.october';
  static const date_picker_months_november = 'date_picker.months.november';
  static const date_picker_months_december = 'date_picker.months.december';
  static const date_picker_months = 'date_picker.months';
  static const date_picker_weekdays_monday = 'date_picker.weekdays.monday';
  static const date_picker_weekdays_tuesday = 'date_picker.weekdays.tuesday';
  static const date_picker_weekdays_wednesday = 'date_picker.weekdays.wednesday';
  static const date_picker_weekdays_thursday = 'date_picker.weekdays.thursday';
  static const date_picker_weekdays_friday = 'date_picker.weekdays.friday';
  static const date_picker_weekdays_saturday = 'date_picker.weekdays.saturday';
  static const date_picker_weekdays_sunday = 'date_picker.weekdays.sunday';
  static const date_picker_weekdays = 'date_picker.weekdays';
  static const date_picker_weekdays_short_monday = 'date_picker.weekdays_short.monday';
  static const date_picker_weekdays_short_tuesday = 'date_picker.weekdays_short.tuesday';
  static const date_picker_weekdays_short_wednesday = 'date_picker.weekdays_short.wednesday';
  static const date_picker_weekdays_short_thursday = 'date_picker.weekdays_short.thursday';
  static const date_picker_weekdays_short_friday = 'date_picker.weekdays_short.friday';
  static const date_picker_weekdays_short_saturday = 'date_picker.weekdays_short.saturday';
  static const date_picker_weekdays_short_sunday = 'date_picker.weekdays_short.sunday';
  static const date_picker_weekdays_short = 'date_picker.weekdays_short';
  static const date_picker = 'date_picker';
  static const errors_network_error = 'errors.network_error';
  static const errors_server_error = 'errors.server_error';
  static const errors_unknown_error = 'errors.unknown_error';
  static const errors_timeout_error = 'errors.timeout_error';
  static const errors_unauthorized = 'errors.unauthorized';
  static const errors_forbidden = 'errors.forbidden';
  static const errors_not_found = 'errors.not_found';
  static const errors_bad_request = 'errors.bad_request';
  static const errors_internal_server_error = 'errors.internal_server_error';
  static const errors_no_internet = 'errors.no_internet';
  static const errors_payment_process_error = 'errors.payment_process_error';
  static const errors_no_data_available = 'errors.no_data_available';
  static const errors_connection_timeout = 'errors.connection_timeout';
  static const errors_send_timeout = 'errors.send_timeout';
  static const errors_receive_timeout = 'errors.receive_timeout';
  static const errors_request_cancelled = 'errors.request_cancelled';
  static const errors_connection_error = 'errors.connection_error';
  static const errors_bad_request_detailed = 'errors.bad_request_detailed';
  static const errors_unauthorized_detailed = 'errors.unauthorized_detailed';
  static const errors_forbidden_detailed = 'errors.forbidden_detailed';
  static const errors_not_found_detailed = 'errors.not_found_detailed';
  static const errors_conflict = 'errors.conflict';
  static const errors_validation_error = 'errors.validation_error';
  static const errors_too_many_requests = 'errors.too_many_requests';
  static const errors_internal_server_detailed = 'errors.internal_server_detailed';
  static const errors_bad_gateway = 'errors.bad_gateway';
  static const errors_service_unavailable = 'errors.service_unavailable';
  static const errors_server_error_with_code = 'errors.server_error_with_code';
  static const errors_generic_error = 'errors.generic_error';
  static const errors_network_check_failed = 'errors.network_check_failed';
  static const errors_network_connection_error = 'errors.network_connection_error';
  static const errors_demo_login_error = 'errors.demo_login_error';
  static const errors = 'errors';
  static const image_errors_unsupported_format = 'image_errors.unsupported_format';
  static const image_errors_network_error = 'image_errors.network_error';
  static const image_errors_corrupted_image = 'image_errors.corrupted_image';
  static const image_errors_image_not_found = 'image_errors.image_not_found';
  static const image_errors_loading_error = 'image_errors.loading_error';
  static const image_errors = 'image_errors';
  static const dialogs_empty_square_status_empty = 'dialogs.empty_square.status_empty';
  static const dialogs_empty_square_description = 'dialogs.empty_square.description';
  static const dialogs_empty_square_mark_as_empty = 'dialogs.empty_square.mark_as_empty';
  static const dialogs_empty_square_place_number = 'dialogs.empty_square.place_number';
  static const dialogs_empty_square = 'dialogs.empty_square';
  static const dialogs_contact_call_button = 'dialogs.contact.call_button';
  static const dialogs_contact = 'dialogs.contact';
  static const dialogs_loading_default_message = 'dialogs.loading.default_message';
  static const dialogs_loading_cancel = 'dialogs.loading.cancel';
  static const dialogs_loading_hiding_dialog = 'dialogs.loading.hiding_dialog';
  static const dialogs_loading_cannot_pop = 'dialogs.loading.cannot_pop';
  static const dialogs_loading = 'dialogs.loading';
  static const dialogs_cash_payment_cancel = 'dialogs.cash_payment.cancel';
  static const dialogs_cash_payment_confirm_error = 'dialogs.cash_payment.confirm_error';
  static const dialogs_cash_payment_process_error = 'dialogs.cash_payment.process_error';
  static const dialogs_cash_payment_amount_format_comment = 'dialogs.cash_payment.amount_format_comment';
  static const dialogs_cash_payment = 'dialogs.cash_payment';
  static const dialogs_payment_status_creating = 'dialogs.payment_status.creating';
  static const dialogs_payment_status_existing_payment_found = 'dialogs.payment_status.existing_payment_found';
  static const dialogs_payment_status_payment_created = 'dialogs.payment_status.payment_created';
  static const dialogs_payment_status_confirming = 'dialogs.payment_status.confirming';
  static const dialogs_payment_status_paid = 'dialogs.payment_status.paid';
  static const dialogs_payment_status_error_occurred = 'dialogs.payment_status.error_occurred';
  static const dialogs_payment_status_pending_payment_notice = 'dialogs.payment_status.pending_payment_notice';
  static const dialogs_payment_status_unknown_error = 'dialogs.payment_status.unknown_error';
  static const dialogs_payment_status_delete_payment = 'dialogs.payment_status.delete_payment';
  static const dialogs_payment_status_accept_payment = 'dialogs.payment_status.accept_payment';
  static const dialogs_payment_status_show_qr_code = 'dialogs.payment_status.show_qr_code';
  static const dialogs_payment_status_continue = 'dialogs.payment_status.continue';
  static const dialogs_payment_status_close = 'dialogs.payment_status.close';
  static const dialogs_payment_status_retry = 'dialogs.payment_status.retry';
  static const dialogs_payment_status_cheque_number = 'dialogs.payment_status.cheque_number';
  static const dialogs_payment_status_created_at = 'dialogs.payment_status.created_at';
  static const dialogs_payment_status_days_count = 'dialogs.payment_status.days_count';
  static const dialogs_payment_status_status = 'dialogs.payment_status.status';
  static const dialogs_payment_status_status_pending = 'dialogs.payment_status.status_pending';
  static const dialogs_payment_status_status_new = 'dialogs.payment_status.status_new';
  static const dialogs_payment_status_days_unit = 'dialogs.payment_status.days_unit';
  static const dialogs_payment_status_places_label = 'dialogs.payment_status.places_label';
  static const dialogs_payment_status_delete_error = 'dialogs.payment_status.delete_error';
  static const dialogs_payment_status_delete_error_with_message = 'dialogs.payment_status.delete_error_with_message';
  static const dialogs_payment_status_creation_error = 'dialogs.payment_status.creation_error';
  static const dialogs_payment_status_creation_error_with_message = 'dialogs.payment_status.creation_error_with_message';
  static const dialogs_payment_status_confirm_error = 'dialogs.payment_status.confirm_error';
  static const dialogs_payment_status_process_error = 'dialogs.payment_status.process_error';
  static const dialogs_payment_status_unconfirmed_payment_exists = 'dialogs.payment_status.unconfirmed_payment_exists';
  static const dialogs_payment_status_success_status = 'dialogs.payment_status.success_status';
  static const dialogs_payment_status_qr_show_button = 'dialogs.payment_status.qr_show_button';
  static const dialogs_payment_status_grid_refresh_success = 'dialogs.payment_status.grid_refresh_success';
  static const dialogs_payment_status_grid_refresh_error = 'dialogs.payment_status.grid_refresh_error';
  static const dialogs_payment_status_date_format_comment = 'dialogs.payment_status.date_format_comment';
  static const dialogs_payment_status_time_empty = 'dialogs.payment_status.time_empty';
  static const dialogs_payment_status_date_contains_space = 'dialogs.payment_status.date_contains_space';
  static const dialogs_payment_status_date_parts_separator = 'dialogs.payment_status.date_parts_separator';
  static const dialogs_payment_status_date_part_index_0 = 'dialogs.payment_status.date_part_index_0';
  static const dialogs_payment_status_date_part_index_1 = 'dialogs.payment_status.date_part_index_1';
  static const dialogs_payment_status_date_format_conversion = 'dialogs.payment_status.date_format_conversion';
  static const dialogs_payment_status_date_separator = 'dialogs.payment_status.date_separator';
  static const dialogs_payment_status_formatted_date_pattern = 'dialogs.payment_status.formatted_date_pattern';
  static const dialogs_payment_status_formatted_datetime = 'dialogs.payment_status.formatted_datetime';
  static const dialogs_payment_status_cheque_id_display = 'dialogs.payment_status.cheque_id_display';
  static const dialogs_payment_status_days_display = 'dialogs.payment_status.days_display';
  static const dialogs_payment_status = 'dialogs.payment_status';
  static const dialogs_place_check_checking = 'dialogs.place_check.checking';
  static const dialogs_place_check_please_wait = 'dialogs.place_check.please_wait';
  static const dialogs_place_check_success_message = 'dialogs.place_check.success_message';
  static const dialogs_place_check_error_occurred = 'dialogs.place_check.error_occurred';
  static const dialogs_place_check_unknown_error = 'dialogs.place_check.unknown_error';
  static const dialogs_place_check_max_retries_reached = 'dialogs.place_check.max_retries_reached';
  static const dialogs_place_check_attempt_count = 'dialogs.place_check.attempt_count';
  static const dialogs_place_check_cancel = 'dialogs.place_check.cancel';
  static const dialogs_place_check_close = 'dialogs.place_check.close';
  static const dialogs_place_check_retry = 'dialogs.place_check.retry';
  static const dialogs_place_check_check_error = 'dialogs.place_check.check_error';
  static const dialogs_place_check_unexpected_error = 'dialogs.place_check.unexpected_error';
  static const dialogs_place_check = 'dialogs.place_check';
  static const dialogs_qr_payment_title = 'dialogs.qr_payment.title';
  static const dialogs_qr_payment_place_number = 'dialogs.qr_payment.place_number';
  static const dialogs_qr_payment_generating_qr = 'dialogs.qr_payment.generating_qr';
  static const dialogs_qr_payment_scan_instruction = 'dialogs.qr_payment.scan_instruction';
  static const dialogs_qr_payment_check_payment = 'dialogs.qr_payment.check_payment';
  static const dialogs_qr_payment_retry = 'dialogs.qr_payment.retry';
  static const dialogs_qr_payment_creation_error = 'dialogs.qr_payment.creation_error';
  static const dialogs_qr_payment_creation_error_with_message = 'dialogs.qr_payment.creation_error_with_message';
  static const dialogs_qr_payment_qr_generation_error = 'dialogs.qr_payment.qr_generation_error';
  static const dialogs_qr_payment_payment_not_found = 'dialogs.qr_payment.payment_not_found';
  static const dialogs_qr_payment_check_error = 'dialogs.qr_payment.check_error';
  static const dialogs_qr_payment_brightness_error = 'dialogs.qr_payment.brightness_error';
  static const dialogs_qr_payment_brightness_restore_error = 'dialogs.qr_payment.brightness_restore_error';
  static const dialogs_qr_payment_error_message_empty = 'dialogs.qr_payment.error_message_empty';
  static const dialogs_qr_payment_service_id_comment = 'dialogs.qr_payment.service_id_comment';
  static const dialogs_qr_payment_merchant_id_comment = 'dialogs.qr_payment.merchant_id_comment';
  static const dialogs_qr_payment_merchant_user_id_comment = 'dialogs.qr_payment.merchant_user_id_comment';
  static const dialogs_qr_payment = 'dialogs.qr_payment';
  static const dialogs_terminal_payment_paid_button = 'dialogs.terminal_payment.paid_button';
  static const dialogs_terminal_payment_cancel = 'dialogs.terminal_payment.cancel';
  static const dialogs_terminal_payment_confirm_error = 'dialogs.terminal_payment.confirm_error';
  static const dialogs_terminal_payment_process_error = 'dialogs.terminal_payment.process_error';
  static const dialogs_terminal_payment_amount_format_comment = 'dialogs.terminal_payment.amount_format_comment';
  static const dialogs_terminal_payment = 'dialogs.terminal_payment';
  static const dialogs_square_dialog_payment_days_question = 'dialogs.square_dialog.payment_days_question';
  static const dialogs_square_dialog_payment_button = 'dialogs.square_dialog.payment_button';
  static const dialogs_square_dialog_verified_button = 'dialogs.square_dialog.verified_button';
  static const dialogs_square_dialog_mark_empty_button = 'dialogs.square_dialog.mark_empty_button';
  static const dialogs_square_dialog_select_payment_method = 'dialogs.square_dialog.select_payment_method';
  static const dialogs_square_dialog_pay_with_click = 'dialogs.square_dialog.pay_with_click';
  static const dialogs_square_dialog_pay_with_cash = 'dialogs.square_dialog.pay_with_cash';
  static const dialogs_square_dialog_pay_with_terminal = 'dialogs.square_dialog.pay_with_terminal';
  static const dialogs_square_dialog_seller_placeholder = 'dialogs.square_dialog.seller_placeholder';
  static const dialogs_square_dialog_currency_uzs = 'dialogs.square_dialog.currency_uzs';
  static const dialogs_square_dialog_square_meter = 'dialogs.square_dialog.square_meter';
  static const dialogs_square_dialog_debug_no_seller_payment = 'dialogs.square_dialog.debug_no_seller_payment';
  static const dialogs_square_dialog_debug_no_seller_cash = 'dialogs.square_dialog.debug_no_seller_cash';
  static const dialogs_square_dialog_debug_no_seller_terminal = 'dialogs.square_dialog.debug_no_seller_terminal';
  static const dialogs_square_dialog_demo_place_id = 'dialogs.square_dialog.demo_place_id';
  static const dialogs_square_dialog_debug_checking_conditions = 'dialogs.square_dialog.debug_checking_conditions';
  static const dialogs_square_dialog_debug_api_mode = 'dialogs.square_dialog.debug_api_mode';
  static const dialogs_square_dialog_debug_squares_info = 'dialogs.square_dialog.debug_squares_info';
  static const dialogs_square_dialog_debug_seller_details = 'dialogs.square_dialog.debug_seller_details';
  static const dialogs_square_dialog_debug_final_result = 'dialogs.square_dialog.debug_final_result';
  static const dialogs_square_dialog_debug_legacy_mode = 'dialogs.square_dialog.debug_legacy_mode';
  static const dialogs_square_dialog_debug_square_info = 'dialogs.square_dialog.debug_square_info';
  static const dialogs_square_dialog_debug_seller_found_grouped = 'dialogs.square_dialog.debug_seller_found_grouped';
  static const dialogs_square_dialog_debug_seller_null_grouped = 'dialogs.square_dialog.debug_seller_null_grouped';
  static const dialogs_square_dialog_debug_seller_found_single = 'dialogs.square_dialog.debug_seller_found_single';
  static const dialogs_square_dialog_debug_no_seller_found = 'dialogs.square_dialog.debug_no_seller_found';
  static const dialogs_square_dialog_debug_demo_seller_warning = 'dialogs.square_dialog.debug_demo_seller_warning';
  static const dialogs_square_dialog_demo_user_id = 'dialogs.square_dialog.demo_user_id';
  static const dialogs_square_dialog_debt_amount = 'dialogs.square_dialog.debt_amount';
  static const dialogs_square_dialog_total_amount = 'dialogs.square_dialog.total_amount';
  static const dialogs_square_dialog = 'dialogs.square_dialog';
  static const dialogs = 'dialogs';
  static const nazoratchi_reports_empty_square_report_title = 'nazoratchi.reports.empty_square_report.title';
  static const nazoratchi_reports_empty_square_report_image_upload = 'nazoratchi.reports.empty_square_report.image_upload';
  static const nazoratchi_reports_empty_square_report_image_upload_subtitle = 'nazoratchi.reports.empty_square_report.image_upload_subtitle';
  static const nazoratchi_reports_empty_square_report_description = 'nazoratchi.reports.empty_square_report.description';
  static const nazoratchi_reports_empty_square_report_submit = 'nazoratchi.reports.empty_square_report.submit';
  static const nazoratchi_reports_empty_square_report_hint_text = 'nazoratchi.reports.empty_square_report.hint_text';
  static const nazoratchi_reports_empty_square_report_quick_tags_empty_place = 'nazoratchi.reports.empty_square_report.quick_tags.empty_place';
  static const nazoratchi_reports_empty_square_report_quick_tags_unusable_place = 'nazoratchi.reports.empty_square_report.quick_tags.unusable_place';
  static const nazoratchi_reports_empty_square_report_quick_tags_needs_repair = 'nazoratchi.reports.empty_square_report.quick_tags.needs_repair';
  static const nazoratchi_reports_empty_square_report_quick_tags_dirty_place = 'nazoratchi.reports.empty_square_report.quick_tags.dirty_place';
  static const nazoratchi_reports_empty_square_report_quick_tags_broken_equipment = 'nazoratchi.reports.empty_square_report.quick_tags.broken_equipment';
  static const nazoratchi_reports_empty_square_report_quick_tags = 'nazoratchi.reports.empty_square_report.quick_tags';
  static const nazoratchi_reports_empty_square_report_image_picker_select_image = 'nazoratchi.reports.empty_square_report.image_picker.select_image';
  static const nazoratchi_reports_empty_square_report_image_picker_camera = 'nazoratchi.reports.empty_square_report.image_picker.camera';
  static const nazoratchi_reports_empty_square_report_image_picker_gallery = 'nazoratchi.reports.empty_square_report.image_picker.gallery';
  static const nazoratchi_reports_empty_square_report_image_picker_error_selecting_image = 'nazoratchi.reports.empty_square_report.image_picker.error_selecting_image';
  static const nazoratchi_reports_empty_square_report_image_picker_camera_preview_message = 'nazoratchi.reports.empty_square_report.image_picker.camera_preview_message';
  static const nazoratchi_reports_empty_square_report_image_picker = 'nazoratchi.reports.empty_square_report.image_picker';
  static const nazoratchi_reports_empty_square_report_success_dialog_title = 'nazoratchi.reports.empty_square_report.success_dialog.title';
  static const nazoratchi_reports_empty_square_report_success_dialog_message = 'nazoratchi.reports.empty_square_report.success_dialog.message';
  static const nazoratchi_reports_empty_square_report_success_dialog_ok_button = 'nazoratchi.reports.empty_square_report.success_dialog.ok_button';
  static const nazoratchi_reports_empty_square_report_success_dialog = 'nazoratchi.reports.empty_square_report.success_dialog';
  static const nazoratchi_reports_empty_square_report_square_selection_title = 'nazoratchi.reports.empty_square_report.square_selection.title';
  static const nazoratchi_reports_empty_square_report_square_selection = 'nazoratchi.reports.empty_square_report.square_selection';
  static const nazoratchi_reports_empty_square_report = 'nazoratchi.reports.empty_square_report';
  static const nazoratchi_reports = 'nazoratchi.reports';
  static const nazoratchi_statistics_all = 'nazoratchi.statistics.all';
  static const nazoratchi_statistics_plan_in_plan = 'nazoratchi.statistics.plan_in_plan';
  static const nazoratchi_statistics_plan_received = 'nazoratchi.statistics.plan_received';
  static const nazoratchi_statistics_plan_via_click = 'nazoratchi.statistics.plan_via_click';
  static const nazoratchi_statistics_plan_cash = 'nazoratchi.statistics.plan_cash';
  static const nazoratchi_statistics_plan_via_terminal = 'nazoratchi.statistics.plan_via_terminal';
  static const nazoratchi_statistics_plan_debt = 'nazoratchi.statistics.plan_debt';
  static const nazoratchi_statistics_legend_paid_places = 'nazoratchi.statistics.legend_paid_places';
  static const nazoratchi_statistics_legend_unpaid_places = 'nazoratchi.statistics.legend_unpaid_places';
  static const nazoratchi_statistics_legend_unassigned_places = 'nazoratchi.statistics.legend_unassigned_places';
  static const nazoratchi_statistics_legend_empty_places = 'nazoratchi.statistics.legend_empty_places';
  static const nazoratchi_statistics_payment_delivered = 'nazoratchi.statistics.payment_delivered';
  static const nazoratchi_statistics_payment_not_delivered = 'nazoratchi.statistics.payment_not_delivered';
  static const nazoratchi_statistics = 'nazoratchi.statistics';
  static const nazoratchi_payment_terminal_payment_title = 'nazoratchi.payment.terminal_payment_title';
  static const nazoratchi_payment_cash_payment_title = 'nazoratchi.payment.cash_payment_title';
  static const nazoratchi_payment_payment_rejected = 'nazoratchi.payment.payment_rejected';
  static const nazoratchi_payment_ok_button = 'nazoratchi.payment.ok_button';
  static const nazoratchi_payment = 'nazoratchi.payment';
  static const nazoratchi_tuzilma_no_data_available = 'nazoratchi.tuzilma.no_data_available';
  static const nazoratchi_tuzilma_error_occurred = 'nazoratchi.tuzilma.error_occurred';
  static const nazoratchi_tuzilma_retry = 'nazoratchi.tuzilma.retry';
  static const nazoratchi_tuzilma_payment_impossible = 'nazoratchi.tuzilma.payment_impossible';
  static const nazoratchi_tuzilma_no_seller_info = 'nazoratchi.tuzilma.no_seller_info';
  static const nazoratchi_tuzilma_default_category = 'nazoratchi.tuzilma.default_category';
  static const nazoratchi_tuzilma_place_number = 'nazoratchi.tuzilma.place_number';
  static const nazoratchi_tuzilma_default_seller_name = 'nazoratchi.tuzilma.default_seller_name';
  static const nazoratchi_tuzilma_default_empty_description = 'nazoratchi.tuzilma.default_empty_description';
  static const nazoratchi_tuzilma_debt_loading_error = 'nazoratchi.tuzilma.debt_loading_error';
  static const nazoratchi_tuzilma_status_paid = 'nazoratchi.tuzilma.status.paid';
  static const nazoratchi_tuzilma_status_unpaid = 'nazoratchi.tuzilma.status.unpaid';
  static const nazoratchi_tuzilma_status_unassigned = 'nazoratchi.tuzilma.status.unassigned';
  static const nazoratchi_tuzilma_status_empty = 'nazoratchi.tuzilma.status.empty';
  static const nazoratchi_tuzilma_status_unknown = 'nazoratchi.tuzilma.status.unknown';
  static const nazoratchi_tuzilma_status = 'nazoratchi.tuzilma.status';
  static const nazoratchi_tuzilma_errors_network_connection = 'nazoratchi.tuzilma.errors.network_connection';
  static const nazoratchi_tuzilma_errors_network_unavailable = 'nazoratchi.tuzilma.errors.network_unavailable';
  static const nazoratchi_tuzilma_errors_pavilion_load_error = 'nazoratchi.tuzilma.errors.pavilion_load_error';
  static const nazoratchi_tuzilma_errors_blocks_load_error = 'nazoratchi.tuzilma.errors.blocks_load_error';
  static const nazoratchi_tuzilma_errors_unexpected_error = 'nazoratchi.tuzilma.errors.unexpected_error';
  static const nazoratchi_tuzilma_errors_status_update_failed = 'nazoratchi.tuzilma.errors.status_update_failed';
  static const nazoratchi_tuzilma_errors_report_not_sent = 'nazoratchi.tuzilma.errors.report_not_sent';
  static const nazoratchi_tuzilma_errors_image_file_not_found = 'nazoratchi.tuzilma.errors.image_file_not_found';
  static const nazoratchi_tuzilma_errors = 'nazoratchi.tuzilma.errors';
  static const nazoratchi_tuzilma_success_empty_place_marked = 'nazoratchi.tuzilma.success.empty_place_marked';
  static const nazoratchi_tuzilma_success_empty_place_mark_error = 'nazoratchi.tuzilma.success.empty_place_mark_error';
  static const nazoratchi_tuzilma_success = 'nazoratchi.tuzilma.success';
  static const nazoratchi_tuzilma_dialog_unexpected_error = 'nazoratchi.tuzilma.dialog.unexpected_error';
  static const nazoratchi_tuzilma_dialog_place_number = 'nazoratchi.tuzilma.dialog.place_number';
  static const nazoratchi_tuzilma_dialog_total_amount = 'nazoratchi.tuzilma.dialog.total_amount';
  static const nazoratchi_tuzilma_dialog_total_square = 'nazoratchi.tuzilma.dialog.total_square';
  static const nazoratchi_tuzilma_dialog_payment_days_question = 'nazoratchi.tuzilma.dialog.payment_days_question';
  static const nazoratchi_tuzilma_dialog_category = 'nazoratchi.tuzilma.dialog.category';
  static const nazoratchi_tuzilma_dialog_tariff = 'nazoratchi.tuzilma.dialog.tariff';
  static const nazoratchi_tuzilma_dialog_daily_rate = 'nazoratchi.tuzilma.dialog.daily_rate';
  static const nazoratchi_tuzilma_dialog_place_size = 'nazoratchi.tuzilma.dialog.place_size';
  static const nazoratchi_tuzilma_dialog_place_size_meters = 'nazoratchi.tuzilma.dialog.place_size_meters';
  static const nazoratchi_tuzilma_dialog_debt = 'nazoratchi.tuzilma.dialog.debt';
  static const nazoratchi_tuzilma_dialog_last_payment_date = 'nazoratchi.tuzilma.dialog.last_payment_date';
  static const nazoratchi_tuzilma_dialog_last_payment_amount = 'nazoratchi.tuzilma.dialog.last_payment_amount';
  static const nazoratchi_tuzilma_dialog_debt_days = 'nazoratchi.tuzilma.dialog.debt_days';
  static const nazoratchi_tuzilma_dialog_seller_placeholder = 'nazoratchi.tuzilma.dialog.seller_placeholder';
  static const nazoratchi_tuzilma_dialog_currency_uzs = 'nazoratchi.tuzilma.dialog.currency_uzs';
  static const nazoratchi_tuzilma_dialog_square_meter = 'nazoratchi.tuzilma.dialog.square_meter';
  static const nazoratchi_tuzilma_dialog_error_title = 'nazoratchi.tuzilma.dialog.error_title';
  static const nazoratchi_tuzilma_dialog_data_not_loaded = 'nazoratchi.tuzilma.dialog.data_not_loaded';
  static const nazoratchi_tuzilma_dialog_make_payment = 'nazoratchi.tuzilma.dialog.make_payment';
  static const nazoratchi_tuzilma_dialog_checked = 'nazoratchi.tuzilma.dialog.checked';
  static const nazoratchi_tuzilma_dialog_mark_empty_place = 'nazoratchi.tuzilma.dialog.mark_empty_place';
  static const nazoratchi_tuzilma_dialog_select_payment_method = 'nazoratchi.tuzilma.dialog.select_payment_method';
  static const nazoratchi_tuzilma_dialog_pay_with_click = 'nazoratchi.tuzilma.dialog.pay_with_click';
  static const nazoratchi_tuzilma_dialog_pay_with_cash = 'nazoratchi.tuzilma.dialog.pay_with_cash';
  static const nazoratchi_tuzilma_dialog_pay_with_terminal = 'nazoratchi.tuzilma.dialog.pay_with_terminal';
  static const nazoratchi_tuzilma_dialog = 'nazoratchi.tuzilma.dialog';
  static const nazoratchi_tuzilma = 'nazoratchi.tuzilma';
  static const nazoratchi_market_structure_blocks = 'nazoratchi.market_structure.blocks';
  static const nazoratchi_market_structure_places = 'nazoratchi.market_structure.places';
  static const nazoratchi_market_structure_total_places = 'nazoratchi.market_structure.total_places';
  static const nazoratchi_market_structure_category = 'nazoratchi.market_structure.category';
  static const nazoratchi_market_structure_tariff = 'nazoratchi.market_structure.tariff';
  static const nazoratchi_market_structure_place_size = 'nazoratchi.market_structure.place_size';
  static const nazoratchi_market_structure_total_square = 'nazoratchi.market_structure.total_square';
  static const nazoratchi_market_structure_daily_rate = 'nazoratchi.market_structure.daily_rate';
  static const nazoratchi_market_structure_meter = 'nazoratchi.market_structure.meter';
  static const nazoratchi_market_structure_debt = 'nazoratchi.market_structure.debt';
  static const nazoratchi_market_structure_total_amount = 'nazoratchi.market_structure.total_amount';
  static const nazoratchi_market_structure_legend_paid = 'nazoratchi.market_structure.legend.paid';
  static const nazoratchi_market_structure_legend_unpaid = 'nazoratchi.market_structure.legend.unpaid';
  static const nazoratchi_market_structure_legend_empty = 'nazoratchi.market_structure.legend.empty';
  static const nazoratchi_market_structure_legend_unassigned = 'nazoratchi.market_structure.legend.unassigned';
  static const nazoratchi_market_structure_legend = 'nazoratchi.market_structure.legend';
  static const nazoratchi_market_structure_detailed = 'nazoratchi.market_structure.detailed';
  static const nazoratchi_market_structure = 'nazoratchi.market_structure';
  static const nazoratchi = 'nazoratchi';
  static const sotuvchi_home_title = 'sotuvchi.home.title';
  static const sotuvchi_home_tariff = 'sotuvchi.home.tariff';
  static const sotuvchi_home = 'sotuvchi.home';
  static const sotuvchi_payment_history_no_payments_title = 'sotuvchi.payment_history.no_payments_title';
  static const sotuvchi_payment_history_no_payments_subtitle = 'sotuvchi.payment_history.no_payments_subtitle';
  static const sotuvchi_payment_history_place_number = 'sotuvchi.payment_history.place_number';
  static const sotuvchi_payment_history_tariff = 'sotuvchi.payment_history.tariff';
  static const sotuvchi_payment_history_paid_date = 'sotuvchi.payment_history.paid_date';
  static const sotuvchi_payment_history = 'sotuvchi.payment_history';
  static const sotuvchi_profile_title = 'sotuvchi.profile.title';
  static const sotuvchi_profile_logout_dialog_title = 'sotuvchi.profile.logout_dialog_title';
  static const sotuvchi_profile_logout_dialog_content = 'sotuvchi.profile.logout_dialog_content';
  static const sotuvchi_profile_logout_dialog_cancel = 'sotuvchi.profile.logout_dialog_cancel';
  static const sotuvchi_profile_logout_dialog_confirm = 'sotuvchi.profile.logout_dialog_confirm';
  static const sotuvchi_profile_image_selection_error = 'sotuvchi.profile.image_selection_error';
  static const sotuvchi_profile_logout_error = 'sotuvchi.profile.logout_error';
  static const sotuvchi_profile_load_error = 'sotuvchi.profile.load_error';
  static const sotuvchi_profile_image_update_error = 'sotuvchi.profile.image_update_error';
  static const sotuvchi_profile_cache_loaded = 'sotuvchi.profile.cache_loaded';
  static const sotuvchi_profile_refresh_success = 'sotuvchi.profile.refresh_success';
  static const sotuvchi_profile_load_failed = 'sotuvchi.profile.load_failed';
  static const sotuvchi_profile_refresh_failed = 'sotuvchi.profile.refresh_failed';
  static const sotuvchi_profile = 'sotuvchi.profile';
  static const sotuvchi_empty_places_load_error = 'sotuvchi.empty_places.load_error';
  static const sotuvchi_empty_places = 'sotuvchi.empty_places';
  static const sotuvchi = 'sotuvchi';
  static const face_control_title = 'face_control.title';
  static const face_control_enter_face = 'face_control.enter_face';
  static const face_control_confirm_face = 'face_control.confirm_face';
  static const face_control_settings = 'face_control.settings';
  static const face_control_about = 'face_control.about';
  static const face_control_refresh = 'face_control.refresh';
  static const face_control_please_refresh_page = 'face_control.please_refresh_page';
  static const face_control_warning_message = 'face_control.warning_message';
  static const face_control_warning_details = 'face_control.warning_details';
  static const face_control_camera_preview_message = 'face_control.camera_preview_message';
  static const face_control_errors_check_internet = 'face_control.errors.check_internet';
  static const face_control_errors_user_not_found = 'face_control.errors.user_not_found';
  static const face_control_errors_unknown_role = 'face_control.errors.unknown_role';
  static const face_control_errors_invalid_user_data = 'face_control.errors.invalid_user_data';
  static const face_control_errors_profile_load_error = 'face_control.errors.profile_load_error';
  static const face_control_errors_download_error = 'face_control.errors.download_error';
  static const face_control_errors_image_capture_error = 'face_control.errors.image_capture_error';
  static const face_control_errors_image_process_error = 'face_control.errors.image_process_error';
  static const face_control_errors_logout_error = 'face_control.errors.logout_error';
  static const face_control_errors = 'face_control.errors';
  static const face_control_settings_page_title = 'face_control.settings_page.title';
  static const face_control_settings_page_camera_lens = 'face_control.settings_page.camera_lens';
  static const face_control_settings_page_front = 'face_control.settings_page.front';
  static const face_control_settings_page_thresholds = 'face_control.settings_page.thresholds';
  static const face_control_settings_page_liveness_level = 'face_control.settings_page.liveness_level';
  static const face_control_settings_page_liveness_threshold = 'face_control.settings_page.liveness_threshold';
  static const face_control_settings_page_identify_threshold = 'face_control.settings_page.identify_threshold';
  static const face_control_settings_page_reset = 'face_control.settings_page.reset';
  static const face_control_settings_page_restore_default = 'face_control.settings_page.restore_default';
  static const face_control_settings_page_clear_all_person = 'face_control.settings_page.clear_all_person';
  static const face_control_settings_page_cancel = 'face_control.settings_page.cancel';
  static const face_control_settings_page_ok = 'face_control.settings_page.ok';
  static const face_control_settings_page = 'face_control.settings_page';
  static const face_control_about_page_title = 'face_control.about_page.title';
  static const face_control_about_page_developer_message = 'face_control.about_page.developer_message';
  static const face_control_about_page_email = 'face_control.about_page.email';
  static const face_control_about_page_phone = 'face_control.about_page.phone';
  static const face_control_about_page_telegram = 'face_control.about_page.telegram';
  static const face_control_about_page_github = 'face_control.about_page.github';
  static const face_control_about_page = 'face_control.about_page';
  static const face_control_functional_page_unlock_app = 'face_control.functional_page.unlock_app';
  static const face_control_functional_page_support_info = 'face_control.functional_page.support_info';
  static const face_control_functional_page_time_check = 'face_control.functional_page.time_check';
  static const face_control_functional_page_location_check = 'face_control.functional_page.location_check';
  static const face_control_functional_page_mock_location_check = 'face_control.functional_page.mock_location_check';
  static const face_control_functional_page_waiting = 'face_control.functional_page.waiting';
  static const face_control_functional_page_please_fix_issues = 'face_control.functional_page.please_fix_issues';
  static const face_control_functional_page_time_difference = 'face_control.functional_page.time_difference';
  static const face_control_functional_page = 'face_control.functional_page';
  static const face_control_person_view_approved = 'face_control.person_view.approved';
  static const face_control_person_view_not_approved = 'face_control.person_view.not_approved';
  static const face_control_person_view_admin_only_delete = 'face_control.person_view.admin_only_delete';
  static const face_control_person_view = 'face_control.person_view';
  static const face_control_face_detection_face_control = 'face_control.face_detection.face_control';
  static const face_control_face_detection_error_uploading = 'face_control.face_detection.error_uploading';
  static const face_control_face_detection_admin_not_approved = 'face_control.face_detection.admin_not_approved';
  static const face_control_face_detection_uploaded = 'face_control.face_detection.uploaded';
  static const face_control_face_detection_approved = 'face_control.face_detection.approved';
  static const face_control_face_detection_photo_uploaded = 'face_control.face_detection.photo_uploaded';
  static const face_control_face_detection_similarity = 'face_control.face_detection.similarity';
  static const face_control_face_detection_liveness = 'face_control.face_detection.liveness';
  static const face_control_face_detection_liveness_calculating = 'face_control.face_detection.liveness_calculating';
  static const face_control_face_detection_upload_error_message = 'face_control.face_detection.upload_error_message';
  static const face_control_face_detection = 'face_control.face_detection';
  static const face_control = 'face_control';
  static const camera_title = 'camera.title';
  static const camera_selected_image = 'camera.selected_image';
  static const camera_captured_image = 'camera.captured_image';
  static const camera_camera_preparing = 'camera.camera_preparing';
  static const camera_gallery = 'camera.gallery';
  static const camera_settings = 'camera.settings';
  static const camera_camera_settings = 'camera.camera_settings';
  static const camera_hd_quality = 'camera.hd_quality';
  static const camera_hd_quality_subtitle = 'camera.hd_quality_subtitle';
  static const camera_grid_lines = 'camera.grid_lines';
  static const camera_grid_lines_subtitle = 'camera.grid_lines_subtitle';
  static const camera_auto_save = 'camera.auto_save';
  static const camera_auto_save_subtitle = 'camera.auto_save_subtitle';
  static const camera_image_format = 'camera.image_format';
  static const camera_image_format_current = 'camera.image_format_current';
  static const camera_select_image_format = 'camera.select_image_format';
  static const camera_jpeg_description = 'camera.jpeg_description';
  static const camera_png_description = 'camera.png_description';
  static const camera_cancel = 'camera.cancel';
  static const camera_image_saved_to_gallery = 'camera.image_saved_to_gallery';
  static const camera_save_error = 'camera.save_error';
  static const camera_gallery_selection_error = 'camera.gallery_selection_error';
  static const camera_camera_error = 'camera.camera_error';
  static const camera_rotation_error = 'camera.rotation_error';
  static const camera_settings_title = 'camera.settings_title';
  static const camera_hd_quality = 'camera.hd_quality';
  static const camera_hd_quality_subtitle = 'camera.hd_quality_subtitle';
  static const camera_grid_lines = 'camera.grid_lines';
  static const camera_grid_lines_subtitle = 'camera.grid_lines_subtitle';
  static const camera_auto_save = 'camera.auto_save';
  static const camera_auto_save_subtitle = 'camera.auto_save_subtitle';
  static const camera_image_format = 'camera.image_format';
  static const camera_image_format_current = 'camera.image_format_current';
  static const camera_select_image_format = 'camera.select_image_format';
  static const camera_jpeg_description = 'camera.jpeg_description';
  static const camera_png_description = 'camera.png_description';
  static const camera_cancel = 'camera.cancel';
  static const camera_gallery_selection_error = 'camera.gallery_selection_error';
  static const camera_image_saved_to_gallery = 'camera.image_saved_to_gallery';
  static const camera_save_error = 'camera.save_error';
  static const camera_capture_error = 'camera.capture_error';
  static const camera = 'camera';
  static const location_turn_on_location = 'location.turn_on_location';
  static const location_give_location_permission = 'location.give_location_permission';
  static const location_fraud_location = 'location.fraud_location';
  static const location_turn_on_location_message = 'location.turn_on_location_message';
  static const location_give_location_permission_message = 'location.give_location_permission_message';
  static const location_fraud_location_message = 'location.fraud_location_message';
  static const location = 'location';
  static const statistics_history_error_occurred = 'statistics_history.error_occurred';
  static const statistics_history_load_more_error = 'statistics_history.load_more_error';
  static const statistics_history_data_load_error = 'statistics_history.data_load_error';
  static const statistics_history_retry = 'statistics_history.retry';
  static const statistics_history_no_data_found = 'statistics_history.no_data_found';
  static const statistics_history_places_header = 'statistics_history.places_header';
  static const statistics_history_amount_header = 'statistics_history.amount_header';
  static const statistics_history_payment_history_load_error = 'statistics_history.payment_history_load_error';
  static const statistics_history_statistics_load_error = 'statistics_history.statistics_load_error';
  static const statistics_history_invalid_response_format = 'statistics_history.invalid_response_format';
  static const statistics_history = 'statistics_history';
  static const face_control_errors_user_not_found = 'face_control.errors.user_not_found';
  static const face_control_errors_unknown_role = 'face_control.errors.unknown_role';
  static const face_control_errors_invalid_user_data = 'face_control.errors.invalid_user_data';
  static const face_control_errors_profile_load_error = 'face_control.errors.profile_load_error';
  static const face_control_errors_download_error = 'face_control.errors.download_error';
  static const face_control_errors_image_capture_error = 'face_control.errors.image_capture_error';
  static const face_control_errors_image_process_error = 'face_control.errors.image_process_error';
  static const face_control_errors_logout_error = 'face_control.errors.logout_error';
  static const face_control_errors_face_not_detected = 'face_control.errors.face_not_detected';
  static const face_control_errors = 'face_control.errors';
  static const face_control = 'face_control';
  static const payment_errors_bad_request = 'payment.errors.bad_request';
  static const payment_errors_unauthorized = 'payment.errors.unauthorized';
  static const payment_errors_not_found = 'payment.errors.not_found';
  static const payment_errors_conflict = 'payment.errors.conflict';
  static const payment_errors_server_error = 'payment.errors.server_error';
  static const payment_errors_payment_error = 'payment.errors.payment_error';
  static const payment_errors_unexpected_error = 'payment.errors.unexpected_error';
  static const payment_errors = 'payment.errors';
  static const payment_types_cash = 'payment.types.cash';
  static const payment_types_terminal = 'payment.types.terminal';
  static const payment_types_qr = 'payment.types.qr';
  static const payment_types_cash_short = 'payment.types.cash_short';
  static const payment_types_terminal_short = 'payment.types.terminal_short';
  static const payment_types_qr_short = 'payment.types.qr_short';
  static const payment_types = 'payment.types';
  static const payment = 'payment';
  static const market_structure_no_pavilions_subtitle = 'market_structure.no_pavilions_subtitle';

}
